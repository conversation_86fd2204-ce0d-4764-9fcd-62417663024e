@extends('layout/base')

@section('body')
    <!-- Hero Section -->
    <div class="hero-section">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-8 text-center">
                    <div class="hero-content animate-fade-in-up">
                        <div class="hero-icon">
                            <i class="fas fa-link"></i>
                        </div>
                        <h1 class="hero-title">Autologin Link Generator</h1>
                        <p class="hero-subtitle">Create secure autologin links for seamless user authentication</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="container pb-5">
        <div class="row justify-content-center">
            <div class="col-lg-10 col-xl-8">
                <div class="card modern-card shadow-modern animate-slide-in-right">
                    <div class="card-header modern-header">
                        <div class="header-content">
                            <div class="header-icon">
                                <i class="fas fa-cog"></i>
                            </div>
                            <div class="header-text" style="display: flex; flex-direction: column;">
                                <h3 class="card-title mb-0">Link Configuration</h3>
                                <p class="card-subtitle">Fill in the details to generate your autologin link</p>
                            </div>
                        </div>
                    </div>

                    <div class="card-body modern-body">
                        <form id="autologin-link-generator" class="modern-form">
                            <!-- Primary Configuration -->
                            <div class="form-section">
                                <div class="section-header">
                                    <h5 class="section-title">
                                        <i class="fas fa-server text-primary"></i>
                                        Server Configuration
                                    </h5>
                                    <p class="section-description">Configure your website domain and authentication key</p>
                                </div>

                                <div class="row">
                                    <div class="form-group col-md-6">
                                        <label class="form-label">
                                            <i class="fas fa-globe"></i>
                                            Website Domain
                                            <span class="required">*</span>
                                        </label>
                                        <div class="input-group">
                                            <div class="input-group-prepend">
                                                <span class="input-group-text">https://</span>
                                            </div>
                                            <input type="text" name="domain" placeholder="example.com"
                                                   class="form-control modern-input" required>
                                        </div>
                                        <small class="form-text text-muted">Enter your website domain without protocol</small>
                                    </div>

                                    <div class="form-group col-md-6">
                                        <label class="form-label">
                                            <i class="fas fa-key"></i>
                                            Autologin Key
                                            <span class="required">*</span>
                                        </label>
                                        <div class="input-wrapper">
                                            <input type="password" name="key" placeholder="Enter your secure autologin key"
                                                   class="form-control modern-input" required>
                                            <button type="button" class="input-toggle" onclick="togglePasswordVisibility(this)">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                        </div>
                                        <small class="form-text text-muted">Your secret authentication key</small>
                                    </div>
                                </div>
                            </div>

                            <!-- User Information -->
                            <div class="form-section">
                                <div class="section-header">
                                    <h5 class="section-title">
                                        <i class="fas fa-user text-info"></i>
                                        User Information
                                    </h5>
                                    <p class="section-description">Specify the user details for the autologin link</p>
                                </div>

                                <div class="row">
                                    <div class="form-group col-md-12">
                                        <label class="form-label">
                                            <i class="fas fa-envelope"></i>
                                            Email Address
                                            <span class="required">*</span>
                                        </label>
                                        <input type="email" name="email" placeholder="<EMAIL>"
                                               class="form-control modern-input" required>
                                        <small class="form-text text-muted">The user's email address for authentication</small>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="form-group col-md-6">
                                        <label class="form-label">
                                            <i class="fas fa-user"></i>
                                            First Name
                                            <span class="optional">(Optional)</span>
                                        </label>
                                        <input type="text" name="first_name" placeholder="John"
                                               class="form-control modern-input">
                                        <small class="form-text text-muted">User's first name</small>
                                    </div>

                                    <div class="form-group col-md-6">
                                        <label class="form-label">
                                            <i class="fas fa-user"></i>
                                            Last Name
                                            <span class="optional">(Optional)</span>
                                        </label>
                                        <input type="text" name="last_name" placeholder="Doe"
                                               class="form-control modern-input">
                                        <small class="form-text text-muted">User's last name</small>
                                    </div>
                                </div>
                            </div>

                            <!-- Action Button -->
                            <div class="form-actions">
                                <button type="submit" class="btn btn-primary btn-lg modern-btn">
                                    <i class="fas fa-magic"></i>
                                    <span>Generate Autologin Link</span>
                                    <div class="btn-loader">
                                        <div class="spinner-border spinner-border-sm" role="status">
                                            <span class="sr-only">Loading...</span>
                                        </div>
                                    </div>
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
@section('css')
<style>
/* Hero Section Styles */
.hero-section {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%);
    padding: 4rem 0 6rem;
    margin-bottom: -3rem;
    position: relative;
    overflow: hidden;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
}

.hero-content {
    position: relative;
    z-index: 2;
}

.hero-icon {
    width: 80px;
    height: 80px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 2rem;
    backdrop-filter: blur(10px);
    border: 2px solid rgba(255, 255, 255, 0.3);
}

.hero-icon i {
    font-size: 2rem;
    color: white;
}

.hero-title {
    color: white !important;
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 1rem;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.hero-subtitle {
    color: rgba(255, 255, 255, 0.9);
    font-size: 1.25rem;
    font-weight: 400;
    margin-bottom: 0;
}

/* Modern Card Styles */
.modern-card {
    border: none !important;
    border-radius: 1.5rem !important;
    overflow: hidden;
    background: var(--bg-primary);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1) !important;
    transition: all 0.3s ease;
}

.modern-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 30px 60px rgba(0, 0, 0, 0.15) !important;
}

.modern-header {
    background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-tertiary) 100%) !important;
    border-bottom: 1px solid var(--border-color) !important;
    padding: 2rem !important;
}

.header-content {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.header-icon {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.25rem;
}

.header-text h3 {
    color: var(--text-primary) !important;
    font-size: 1.5rem;
    font-weight: 600;
}

.card-subtitle {
    color: var(--text-muted) !important;
    font-size: 0.95rem;
    margin: 0;
}

.modern-body {
    padding: 2.5rem !important;
}

/* Form Section Styles */
.form-section {
    margin-bottom: 3rem;
    padding: 1.5rem;
    background: var(--bg-tertiary);
    border-radius: 1rem;
    border: 1px solid var(--border-light);
}

.form-section:last-child {
    margin-bottom: 0;
}

.section-header {
    margin-bottom: 2rem;
    text-align: center;
}

.section-title {
    color: var(--text-primary) !important;
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.section-description {
    color: var(--text-muted);
    font-size: 0.9rem;
    margin: 0;
}

/* Enhanced Form Controls */
.form-label {
    font-weight: 600 !important;
    color: var(--text-secondary) !important;
    margin-bottom: 0.75rem !important;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
}

.form-label i {
    color: var(--primary-color);
    width: 16px;
}

.required {
    color: var(--danger-color);
    font-weight: 500;
}

.optional {
    color: var(--text-muted);
    font-weight: 400;
    font-size: 0.8rem;
}

.modern-input {
    border: 2px solid var(--border-color) !important;
    border-radius: 0.75rem !important;
    padding: 1rem 1.25rem !important;
    font-size: 0.95rem !important;
    transition: all 0.3s ease !important;
    background: var(--bg-primary) !important;
    color: var(--text-primary) !important;
}

.modern-input:focus {
    border-color: var(--primary-color) !important;
    box-shadow: 0 0 0 4px rgba(79, 70, 229, 0.1) !important;
    transform: translateY(-1px);
}

.input-group-text {
    background: var(--bg-tertiary) !important;
    border: 2px solid var(--border-color) !important;
    border-right: none !important;
    color: var(--text-muted) !important;
    font-weight: 500;
    border-radius: 0.75rem 0 0 0.75rem !important;
}

.input-group .modern-input {
    border-left: none !important;
    border-radius: 0 0.75rem 0.75rem 0 !important;
}

.input-wrapper {
    position: relative;
}

.input-toggle {
    position: absolute;
    right: 1rem;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: var(--text-muted);
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 0.25rem;
    transition: all 0.2s ease;
}

.input-toggle:hover {
    color: var(--primary-color);
    background: var(--bg-tertiary);
}

/* Modern Button */
.modern-btn {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%) !important;
    border: none !important;
    border-radius: 0.75rem !important;
    padding: 1rem 2rem !important;
    font-size: 1rem !important;
    font-weight: 600 !important;
    color: white !important;
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease !important;
    min-width: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.75rem;
}

.modern-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(79, 70, 229, 0.3) !important;
}

.modern-btn:active {
    transform: translateY(0);
}

.btn-loader {
    display: none;
}

.modern-btn.loading .btn-loader {
    display: block;
}

.modern-btn.loading span,
.modern-btn.loading i {
    display: none;
}

.form-actions {
    text-align: center;
    margin-top: 2rem;
}

/* Result Display */
.result-section {
    margin-top: 2rem;
    padding: 2rem;
    background: linear-gradient(135deg, var(--success-color) 0%, #059669 100%);
    border-radius: 1rem;
    color: white;
    text-align: center;
    opacity: 0;
    transform: translateY(20px);
    transition: all 0.5s ease;
}

.result-section.show {
    opacity: 1;
    transform: translateY(0);
}

.result-title {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.result-link {
    background: rgba(255, 255, 255, 0.2);
    border-radius: 0.5rem;
    padding: 1rem;
    margin: 1rem 0;
    word-break: break-all;
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.result-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-top: 1.5rem;
}

.result-btn {
    background: rgba(255, 255, 255, 0.2) !important;
    border: 1px solid rgba(255, 255, 255, 0.3) !important;
    color: white !important;
    padding: 0.5rem 1rem !important;
    border-radius: 0.5rem !important;
    font-size: 0.9rem !important;
    transition: all 0.2s ease !important;
}

.result-btn:hover {
    background: rgba(255, 255, 255, 0.3) !important;
    color: white !important;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-title {
        font-size: 2rem;
    }

    .hero-subtitle {
        font-size: 1rem;
    }

    .modern-header,
    .modern-body {
        padding: 1.5rem !important;
    }

    .form-section {
        padding: 1rem;
        margin-bottom: 2rem;
    }

    .header-content {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }

    .section-title {
        font-size: 1.1rem;
    }

    .result-actions {
        flex-direction: column;
    }
}
</style>
@endsection

@section('js')
    <script>
        // Enhanced validation patterns
        const validation = {
            domainPattern: /^[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/,
            emailPattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
            keyPattern: /^.{8,}$/ // At least 8 characters
        };

        // Enhanced link generator with better error handling
        const linkGenerator = {
            autologin: function (data) {
                const domain = findData(data, 'domain');
                const key = findData(data, 'key');
                const email = findData(data, 'email');

                let url = `https://${domain}/autologin?key=${encodeURIComponent(key)}&email=${encodeURIComponent(email)}`;

                const firstName = findData(data, 'first_name');
                const lastName = findData(data, 'last_name');

                if (firstName && firstName.length > 0) {
                    url += `&first_name=${encodeURIComponent(firstName)}`;
                }
                if (lastName && lastName.length > 0) {
                    url += `&last_name=${encodeURIComponent(lastName)}`;
                }

                return url;
            },

            showLink: function (url) {
                // Remove existing result section
                $('.result-section').remove();

                // Create new result section
                const resultHtml = `
                    <div class="result-section">
                        <div class="result-title">
                            <i class="fas fa-check-circle"></i>
                            Autologin Link Generated Successfully!
                        </div>
                        <div class="result-link" id="generated-link">${url}</div>
                        <div class="result-actions">
                            <button type="button" class="btn result-btn" onclick="copyToClipboard()">
                                <i class="fas fa-copy"></i> Copy Link
                            </button>
                            <button type="button" class="btn result-btn" onclick="testLink()">
                                <i class="fas fa-external-link-alt"></i> Test Link
                            </button>
                            <button type="button" class="btn result-btn" onclick="generateNew()">
                                <i class="fas fa-redo"></i> Generate New
                            </button>
                        </div>
                    </div>
                `;

                $('.modern-card').append(resultHtml);

                // Animate the result section
                setTimeout(() => {
                    $('.result-section').addClass('show');
                }, 100);

                // Scroll to result
                $('html, body').animate({
                    scrollTop: $('.result-section').offset().top - 100
                }, 500);
            },

            validateForm: function (formData) {
                const errors = [];
                const domain = findData(formData, 'domain');
                const key = findData(formData, 'key');
                const email = findData(formData, 'email');

                if (!domain || !validation.domainPattern.test(domain)) {
                    errors.push('Please enter a valid domain name (e.g., example.com)');
                }

                if (!key || !validation.keyPattern.test(key)) {
                    errors.push('Autologin key must be at least 8 characters long');
                }

                if (!email || !validation.emailPattern.test(email)) {
                    errors.push('Please enter a valid email address');
                }

                return errors;
            }
        };

        // Enhanced form submission with loading states and validation
        $('form#autologin-link-generator').submit(function (e) {
            e.preventDefault();

            const $form = $(this);
            const $submitBtn = $form.find('button[type="submit"]');
            const formData = $form.serializeArray();

            // Validate form
            const errors = linkGenerator.validateForm(formData);

            if (errors.length > 0) {
                showNotification('error', 'Validation Error', errors.join('<br>'));
                return false;
            }

            // Show loading state
            $submitBtn.addClass('loading').prop('disabled', true);

            // Simulate processing time for better UX
            setTimeout(() => {
                try {
                    const generatedUrl = linkGenerator.autologin(formData);
                    linkGenerator.showLink(generatedUrl);
                    showNotification('success', 'Success!', 'Autologin link generated successfully');
                } catch (error) {
                    showNotification('error', 'Error', 'Failed to generate link. Please try again.');
                } finally {
                    $submitBtn.removeClass('loading').prop('disabled', false);
                }
            }, 1000);
        });

        // Utility functions
        function findData(inputData, propertyName) {
            for (const data of inputData) {
                if (data.name === propertyName) {
                    return data.value;
                }
            }
            return '';
        }

        function togglePasswordVisibility(button) {
            const input = $(button).siblings('input');
            const icon = $(button).find('i');

            if (input.attr('type') === 'password') {
                input.attr('type', 'text');
                icon.removeClass('fa-eye').addClass('fa-eye-slash');
            } else {
                input.attr('type', 'password');
                icon.removeClass('fa-eye-slash').addClass('fa-eye');
            }
        }

        function copyToClipboard() {
            const linkText = $('#generated-link').text();

            if (navigator.clipboard) {
                navigator.clipboard.writeText(linkText).then(() => {
                    showNotification('success', 'Copied!', 'Link copied to clipboard');
                }).catch(() => {
                    fallbackCopyToClipboard(linkText);
                });
            } else {
                fallbackCopyToClipboard(linkText);
            }
        }

        function fallbackCopyToClipboard(text) {
            const textArea = document.createElement('textarea');
            textArea.value = text;
            textArea.style.position = 'fixed';
            textArea.style.left = '-999999px';
            textArea.style.top = '-999999px';
            document.body.appendChild(textArea);
            textArea.focus();
            textArea.select();

            try {
                document.execCommand('copy');
                showNotification('success', 'Copied!', 'Link copied to clipboard');
            } catch (err) {
                showNotification('error', 'Error', 'Failed to copy link');
            }

            document.body.removeChild(textArea);
        }

        function testLink() {
            const linkText = $('#generated-link').text();
            window.open(linkText, '_blank');
        }

        function generateNew() {
            $('.result-section').removeClass('show');
            setTimeout(() => {
                $('.result-section').remove();
            }, 500);

            // Reset form
            $('form#autologin-link-generator')[0].reset();

            // Focus first input
            $('input[name="domain"]').focus();
        }

        function showNotification(type, title, message) {
            // Using SweetAlert2 if available, otherwise fallback to alert
            if (typeof Swal !== 'undefined') {
                Swal.fire({
                    icon: type,
                    title: title,
                    html: message,
                    timer: type === 'success' ? 3000 : 5000,
                    timerProgressBar: true,
                    showConfirmButton: type === 'error'
                });
            } else {
                alert(`${title}: ${message.replace(/<br>/g, '\n')}`);
            }
        }

        // Enhanced form interactions
        $(document).ready(function() {
            // Add floating label effect
            $('.modern-input').on('focus blur', function() {
                $(this).toggleClass('focused');
            });

            // Real-time validation feedback
            $('input[name="domain"]').on('blur', function() {
                const value = $(this).val();
                if (value && !validation.domainPattern.test(value)) {
                    $(this).addClass('is-invalid');
                } else {
                    $(this).removeClass('is-invalid');
                }
            });

            $('input[name="email"]').on('blur', function() {
                const value = $(this).val();
                if (value && !validation.emailPattern.test(value)) {
                    $(this).addClass('is-invalid');
                } else {
                    $(this).removeClass('is-invalid');
                }
            });

            $('input[name="key"]').on('blur', function() {
                const value = $(this).val();
                if (value && !validation.keyPattern.test(value)) {
                    $(this).addClass('is-invalid');
                } else {
                    $(this).removeClass('is-invalid');
                }
            });

            // Auto-focus first input
            setTimeout(() => {
                $('input[name="domain"]').focus();
            }, 500);
        });
    </script>
@endsection
